import { useEffect } from "react";
import "./App.css";
import RouterData from "./Routes";
import ErrorBoundary from "./components/Error/ErrorBoundary";
import { initializeGlobalErrorHandler, cleanupGlobalErrorHandler } from "./utils/globalErrorHandler";
import { errorLogger } from "./utils/errorLogger";

function App() {
  useEffect(() => {
    // Initialize global error handling
    initializeGlobalErrorHandler();

    // Log app initialization
    errorLogger.logInfo("Application initialized", {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    });

    // Cleanup on unmount
    return () => {
      cleanupGlobalErrorHandler();
    };
  }, []);

  return (
    <ErrorBoundary
      level="critical"
      showDetails={process.env.NODE_ENV === 'development'}
      onError={(error, errorInfo) => {
        errorLogger.logError(
          "Critical application error",
          error,
          { componentStack: errorInfo.componentStack }
        );
      }}
    >
      <RouterData />
    </ErrorBoundary>
  );
}

export default App;
