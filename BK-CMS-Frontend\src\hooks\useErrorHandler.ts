import { useCallback } from 'react';
import { notification } from 'antd';
import { errorLogger } from '../utils/errorLogger';

interface UseErrorHandlerOptions {
  showNotification?: boolean;
  notificationDuration?: number;
  logLevel?: 'error' | 'warning' | 'info';
  context?: Record<string, any>;
}

interface UseErrorHandlerReturn {
  handleError: (error: Error | string, context?: Record<string, any>) => void;
  handleAsyncError: <T>(
    asyncFn: () => Promise<T>,
    context?: Record<string, any>
  ) => Promise<T | null>;
  logError: (message: string, error?: Error, context?: Record<string, any>) => void;
  logWarning: (message: string, context?: Record<string, any>) => void;
  logInfo: (message: string, context?: Record<string, any>) => void;
}

/**
 * Custom hook for handling errors in React components
 * @param options - Configuration options for error handling
 * @returns Object containing error handling functions
 */
export const useErrorHandler = (
  options: UseErrorHandlerOptions = {}
): UseErrorHandlerReturn => {
  const {
    showNotification = true,
    notificationDuration = 5,
    logLevel = 'error',
    context: defaultContext = {}
  } = options;

  const handleError = useCallback((
    error: Error | string,
    context: Record<string, any> = {}
  ) => {
    const errorMessage = typeof error === 'string' ? error : error.message;
    const errorObj = typeof error === 'string' ? new Error(error) : error;
    
    const combinedContext = { ...defaultContext, ...context };

    // Log the error
    errorLogger.logError(errorMessage, errorObj, combinedContext);

    // Show notification to user if enabled
    if (showNotification) {
      notification.error({
        message: 'Error',
        description: errorMessage,
        duration: notificationDuration,
        placement: 'topRight'
      });
    }
  }, [showNotification, notificationDuration, defaultContext]);

  const handleAsyncError = useCallback(async <T>(
    asyncFn: () => Promise<T>,
    context: Record<string, any> = {}
  ): Promise<T | null> => {
    try {
      return await asyncFn();
    } catch (error) {
      handleError(error as Error, context);
      return null;
    }
  }, [handleError]);

  const logError = useCallback((
    message: string,
    error?: Error,
    context: Record<string, any> = {}
  ) => {
    const combinedContext = { ...defaultContext, ...context };
    errorLogger.logError(message, error, combinedContext);
  }, [defaultContext]);

  const logWarning = useCallback((
    message: string,
    context: Record<string, any> = {}
  ) => {
    const combinedContext = { ...defaultContext, ...context };
    errorLogger.logWarning(message, combinedContext);
  }, [defaultContext]);

  const logInfo = useCallback((
    message: string,
    context: Record<string, any> = {}
  ) => {
    const combinedContext = { ...defaultContext, ...context };
    errorLogger.logInfo(message, combinedContext);
  }, [defaultContext]);

  return {
    handleError,
    handleAsyncError,
    logError,
    logWarning,
    logInfo
  };
};

/**
 * Hook specifically for API error handling
 * @param options - Configuration options
 * @returns Error handling functions optimized for API calls
 */
export const useApiErrorHandler = (
  options: UseErrorHandlerOptions = {}
) => {
  const errorHandler = useErrorHandler({
    ...options,
    context: { type: 'api', ...options.context }
  });

  const handleApiError = useCallback((
    error: any,
    endpoint?: string,
    method?: string
  ) => {
    let errorMessage = 'An API error occurred';
    
    if (error?.response) {
      // API responded with error status
      const status = error.response.status;
      const data = error.response.data;
      
      errorMessage = data?.message || `API Error: ${status}`;
      
      errorHandler.handleError(errorMessage, {
        endpoint,
        method,
        status,
        responseData: data
      });
    } else if (error?.request) {
      // Network error
      errorMessage = 'Network error: Unable to reach the server';
      errorHandler.handleError(errorMessage, {
        endpoint,
        method,
        type: 'network'
      });
    } else {
      // Other error
      errorHandler.handleError(error, { endpoint, method });
    }
  }, [errorHandler]);

  return {
    ...errorHandler,
    handleApiError
  };
};

export default useErrorHandler;
