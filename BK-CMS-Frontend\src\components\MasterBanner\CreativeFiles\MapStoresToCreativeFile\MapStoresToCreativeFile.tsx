import React, { useEffect, useMemo, useRef, useState } from "react";
import {
  Table,
  Button,
  notification,
  Pagination,
  Input,
  InputRef,
  message,
  Tag,
} from "antd";
import { Link, useNavigate, useParams } from "react-router-dom";
import type { TableRowSelection } from "antd/es/table/interface";
import { axiosInstance } from "../../../../apiCalls";
import { Cancel } from "../../../../constants/Constant";
import axios from "axios";
import {
  CreativeSelectStore,
  CreativeSelectStoresResponse,
} from "../../../../types/CreativeFileType/CreativeFileType";
import { useTableFilters } from "../../../../hooks/useTableFilter";
import { SearchOutlined } from "@ant-design/icons";
import FilterButtons from "../../../shared/FilterButton/FilterButton";

const MapStoresToCreativeFile: React.FC = () => {
  const { id } = useParams<{ id: string }>(); // Get creative ID
  const {
    currentPage,
    pageSize,
    filters,
    appliedFilters,
    showClearButtons,
    handlePageChange,
    handleFilterChange,
    clearFilter,
    clearAllFilters,
  } = useTableFilters();
  const navigate = useNavigate();

  // State variables
  const [storeOptions, setStoreOptions] = useState<CreativeSelectStore[]>([]);
  const [selectedStores, setSelectedStores] = useState<Set<number>>(new Set());
  const [initialSelectedStores, setInitialSelectedStores] = useState<
    Set<number>
  >(new Set());
  const [newlySelectedStores, setNewlySelectedStores] = useState<Set<number>>(
    new Set()
  );
  const [deselectedStores, setDeselectedStores] = useState<Set<number>>(
    new Set()
  );
  const [loading, setLoading] = useState<boolean>(false);
  // const [currentPage, setCurrentPage] = useState<number>(1);
  // const [pageSize, setPageSize] = useState<number>(10);
  const [totalCount, setTotalCount] = useState<number>(0);
  // const [searchTerm, setSearchTerm] = useState<string>("");

  const [isFocused, setIsFocused] = useState<boolean>(false);
  const codeInputRef = useRef<InputRef>(null);

  const [searchValue, setSearchValue] = useState<string>("");

  const memoizedFilters = useMemo(() => filters, [filters]);

  // const updateURLParams = useCallback(
  //   (updatedFilters: Record<string, string | number | undefined>) => {
  //     const params = new URLSearchParams();
  //     Object.entries(updatedFilters).forEach(([key, value]) => {
  //       if (value !== undefined) params.set(key, String(value));
  //     });
  //     navigate(`?${params.toString()}`, { replace: true });
  //   },
  //   [navigate]
  // );

  // Fetch stores on component mount or when page changes
  useEffect(() => {
    fetchStores();
  }, [currentPage, pageSize, memoizedFilters]);

  const fetchStores = async () => {
    setLoading(true);
    try {
      const response = await axiosInstance.get<CreativeSelectStoresResponse>(
        `/cms/menu/creative_stores/${id}/`,
        {
          params: {
            page: currentPage,
            page_size: pageSize,
            ...memoizedFilters,
          },
        }
      );

      if (response.data?.objects) {
        const fetchedStores = response.data.objects.map(
          (store: CreativeSelectStore) => ({
            id: store.id,
            name: store.name,
            code: store.code,
            is_creative_selected: store.is_creative_selected,
          })
        );

        setStoreOptions(fetchedStores);
        setTotalCount(response.data.total_count);

        //  Capture initially selected stores
        const initiallySelected = new Set(
          fetchedStores
            .filter((store) => store.is_creative_selected)
            .map((store: CreativeSelectStore) => store.id)
        );

        setInitialSelectedStores(initiallySelected);
        setSelectedStores(
          new Set([...initiallySelected, ...newlySelectedStores])
        );
      } else {
        setStoreOptions([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Error fetching stores:", error);
      notification.error({
        message: "Error",
        description: "Failed to load stores.",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchAllStoreIds = async () => {
    try {
      const response = await axiosInstance.get<CreativeSelectStoresResponse>(
        `cms/menu/creative_stores/${id}/`,
        {
          params: {
            page: 1,
            page_size: 999,
          },
        }
      );
      if (response.status === 200) {
        const selectedStoreIds = response.data?.objects.map(
          (store: CreativeSelectStore) => store.id
        );
        return selectedStoreIds;
      }
      return [];
    } catch (error: any) {
      message.error("Error fetching store ids");
    }
  };

  // useEffect(() => {
  //   const params = new URLSearchParams(location.search);

  //   setCurrentPage(parseInt(params.get("page") || "1", 10));
  //   setPageSize(parseInt(params.get("page_size") || "10", 10));
  // }, [location.search]);

  // const handlePageChange = (page: number, pageSize?: number) => {
  //   setCurrentPage(page);
  //   if (pageSize) setPageSize(pageSize);
  //   updateURLParams({ page, page_size: pageSize });
  // };

  //  Handle row selection logic
  // const rowSelection: TableRowSelection<CreativeSelectStore> = {
  //   selectedRowKeys: Array.from(selectedStores),
  //   onChange: (selectedRowKeys: React.Key[]) => {
  //     setSelectedStores(new Set(selectedRowKeys.map(Number)));
  //   },
  //   onSelect: (record: CreativeSelectStore, selected: boolean) => {
  //     setSelectedStores((prev) => {
  //       const newSet = new Set(prev);

  //       if (selected) {
  //         newSet.add(record.id);

  //         if (!initialSelectedStores.has(record.id)) {
  //           setNewlySelectedStores((prev) => new Set(prev).add(record.id));
  //         }

  //         setDeselectedStores((prev) => {
  //           const updatedDeselected = new Set(prev);
  //           updatedDeselected.delete(record.id);
  //           return updatedDeselected;
  //         });
  //       } else {
  //         newSet.delete(record.id);

  //         setNewlySelectedStores((prev) => {
  //           const updatedNewlySelected = new Set(prev);
  //           updatedNewlySelected.delete(record.id);
  //           return updatedNewlySelected;
  //         });

  //         if (initialSelectedStores.has(record.id)) {
  //           setDeselectedStores((prev) => new Set(prev).add(record.id));
  //         }
  //       }
  //       return newSet;
  //     });
  //   },
  //   onSelectAll: async (
  //     selected: boolean,
  //     selectedRows: CreativeSelectStore[],
  //     changeRows: CreativeSelectStore[]
  //   ) => {
  //     void selectedRows;

  //     if (selected) {
  //       const selectedStoreIds = await fetchAllStoreIds();
  //       console.log(selectedStoreIds);

  //       setSelectedStores((prevSelected) => {
  //         const updatedSelection = new Set(prevSelected);
  //         changeRows.forEach((store) => updatedSelection.add(store.id));
  //         return updatedSelection;
  //       });

  //       setNewlySelectedStores((prev) => {
  //         const updatedNewlySelected = new Set(prev);
  //         changeRows.forEach((store) => {
  //           if (!initialSelectedStores.has(store.id)) {
  //             updatedNewlySelected.add(store.id);
  //           }
  //         });
  //         return updatedNewlySelected;
  //       });

  //       setDeselectedStores((prev) => {
  //         const updatedDeselected = new Set(prev);
  //         changeRows.forEach((store) => updatedDeselected.delete(store.id));
  //         return updatedDeselected;
  //       });

  //       message.success(
  //         `Selected ${selectedStoreIds?.length || 0} stores across all pages`
  //       );
  //     } else {
  //       setSelectedStores((prevSelected) => {
  //         const updatedSelection = new Set(prevSelected);
  //         changeRows.forEach((store) => updatedSelection.delete(store.id));
  //         return updatedSelection;
  //       });

  //       setNewlySelectedStores((prev) => {
  //         const updatedNewlySelected = new Set(prev);
  //         changeRows.forEach((store) => updatedNewlySelected.delete(store.id));
  //         return updatedNewlySelected;
  //       });

  //       setDeselectedStores((prev) => {
  //         const updatedDeselected = new Set(prev);
  //         changeRows.forEach((store) => {
  //           if (initialSelectedStores.has(store.id)) {
  //             updatedDeselected.add(store.id);
  //           }
  //         });
  //         return updatedDeselected;
  //       });
  //     }
  //   },

  //   columnWidth: "5%",
  // };
  const rowSelection: TableRowSelection<CreativeSelectStore> = {
    selectedRowKeys: Array.from(selectedStores), // ✅ Selected across pages

    onChange: (selectedRowKeys: React.Key[]) => {
      setSelectedStores(new Set(selectedRowKeys.map(Number)));
    },

    onSelect: (record: CreativeSelectStore, selected: boolean) => {
      setSelectedStores((prev) => {
        const newSet = new Set(prev);
        if (selected) {
          newSet.add(record.id);
          if (!initialSelectedStores.has(record.id)) {
            setNewlySelectedStores((prev) => new Set(prev).add(record.id));
          }
          setDeselectedStores((prev) => {
            const updated = new Set(prev);
            updated.delete(record.id);
            return updated;
          });
        } else {
          newSet.delete(record.id);
          setNewlySelectedStores((prev) => {
            const updated = new Set(prev);
            updated.delete(record.id);
            return updated;
          });
          if (initialSelectedStores.has(record.id)) {
            setDeselectedStores((prev) => new Set(prev).add(record.id));
          }
        }
        return newSet;
      });
    },

    onSelectAll: async (selected: boolean) => {
      if (selected) {
        const selectedStoreIds = await fetchAllStoreIds();
        if (!selectedStoreIds) return;

        const updatedSet = new Set<number>(selectedStoreIds);
        setSelectedStores(updatedSet);
        setNewlySelectedStores(
          new Set(
            selectedStoreIds.filter((id) => !initialSelectedStores.has(id))
          )
        );
        setDeselectedStores(new Set());
        message.success(
          `Selected ${selectedStoreIds.length} stores across all pages`
        );
      } else {
        const allStoreIds = await fetchAllStoreIds();
        if (!allStoreIds) return;

        const toBeRemoved = new Set<number>();
        for (const id of initialSelectedStores) {
          if (allStoreIds.includes(id)) {
            toBeRemoved.add(id);
          }
        }

        setSelectedStores(new Set());
        setNewlySelectedStores(new Set());
        setDeselectedStores(toBeRemoved);

        message.info(`Deselected all stores across all pages`);
      }
    },

    columnWidth: "5%",
  };

  const handleStoreMapping = async () => {
    if (!id) {
      notification.error({
        message: "Invalid Creative file",
        description: "No ID found for mapping.",
      });
      return;
    }

    // store_id: Includes newly selected stores (not part of initially selected stores)
    const finalStoreIds = Array.from(newlySelectedStores);

    // creative_removed_store_id: Includes initially selected stores that were later deselected
    const removedStoreIds = Array.from(deselectedStores);

    //Construct payload
    const payload = {
      creative_id: [Number(id)],
      store_id: finalStoreIds, // Only newly selected stores
      creative_removed_store_id: removedStoreIds, // Only initially selected & later deselected stores
    };

    try {
      const response = await axiosInstance.post(
        "/cms/menu/store-creatives/",
        payload
      );
      if (response.status === 201 || response.status === 200) {
        notification.success({
          message: "Stores Updated",
          description: "Stores have been successfully updated.",
        });
        navigate(`/creative-file-details/${id}/stores`);
      }
    } catch (error) {
      console.error("Error mapping stores:", error);

      let errorMessage = "Failed to update stores.";

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 400) {
          errorMessage = "Please Select or Deselect at least one store.";
        } else {
          errorMessage =
            error.response?.data?.message ||
            error.response?.data?.error ||
            JSON.stringify(error.response?.data) ||
            errorMessage;
        }
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      notification.error({
        message: "Error",
        description: errorMessage,
      });
    }
  };

  const handleSearchChange = (value: string) => {
    handleFilterChange("search", value);
  };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search") {
      setSearchValue("");
    }
  };

  const clearAllFiltersHandler = () => {
    clearAllFilters();
    setSearchValue("");
  };

  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";

    return (
      text
        // .replace(/[^a-zA-Z0-9 ]/g, " ")
        .replace(/[^a-zA-Z0-9\/\- ]/g, " ")
        .trim()
        .split(" ")
        .map(
          (word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
        )
        .join(" ")
    );
  };

  const handleFocus = () => {
    if (!isFocused) {
      setIsFocused(true);
      codeInputRef.current?.focus();
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const columns = [
    {
      title: "Select All",
      dataIndex: "",
      key: "",
      width: "5%",
      fixed: "left" as "left",
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "40%",
      render: (text: string, record: CreativeSelectStore) =>
        text ? (
          <Link
            className="common-link text-decoration-none"
            to={`/stores/${record.id}/details`}
          >
            {text}
          </Link>
        ) : (
          "N/A"
        ),
    },
    { title: "Code", dataIndex: "code", key: "code", width: "30%" },
  ];

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center heading-title">
        <div>Stores Mapping</div>
      </div>

      <div className="d-flex flex-wrap justify-content-between align-items-center mb-3 mt-4">
        <div className="d-flex flex-wrap align-items-center">
          <div className="search-btn-driver">
            <Input.Search
              ref={codeInputRef}
              placeholder="Search by code & name"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={(value) => {
                if (value.trim()) {
                  handleSearchChange(value);
                }
              }}
              onFocus={handleFocus}
              onBlur={handleBlur}
              prefix={
                <SearchOutlined
                  // style={{ visibility: isFocused ? "visible" : "hidden" }}
                  className={isFocused ? "" : "invisible"}
                />
              }
            />
          </div>
          <div>
            <FilterButtons
              showClearButtons={showClearButtons}
              appliedFilters={appliedFilters}
              clearAllFilters={clearAllFiltersHandler}
              clearFilter={clearFilterHandler}
              formatFilterValue={formatPaymentMethod}
              filters={filters}
            />
          </div>
          {selectedStores.size > 0 && (
            <div>
              <Tag
                className="badge  px- py-2 fs-6 fw-bold font-family-Poppins rounded-pill"
                color="blue"
              >
                ({selectedStores.size}) store
                {selectedStores.size > 1 ? "s" : ""} selected
              </Tag>
            </div>
          )}
        </div>
        <div className="ml-3">
          <Button
            type="primary"
            className="btn-save"
            onClick={handleStoreMapping}
            disabled={loading}
          >
            {loading ? `Saving...` : `Save Selection`}
          </Button>
          <Button
            type="default"
            className="btn-cancel"
            onClick={() => navigate(-1)}
          >
            {Cancel}
          </Button>
        </div>
      </div>

      <>
        <div className="mt-3">
          <Table
            rowSelection={rowSelection}
            dataSource={storeOptions}
            columns={columns}
            loading={loading}
            rowKey="id"
            pagination={false}
            scroll={{ x: "max-content" }}
          />
        </div>

        <div className="pagination">
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            onChange={handlePageChange}
            showSizeChanger
          />
        </div>
      </>
    </div>
  );
};

export default MapStoresToCreativeFile;
