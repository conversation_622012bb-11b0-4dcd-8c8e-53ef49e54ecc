import { notification } from 'antd';

interface ErrorDetails {
  message: string;
  stack?: string;
  timestamp: string;
  userAgent: string;
  url: string;
  errorId: string;
  type: 'unhandledRejection' | 'error' | 'chunkLoadError';
}

class GlobalErrorHandler {
  private static instance: GlobalErrorHandler;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): GlobalErrorHandler {
    if (!GlobalErrorHandler.instance) {
      GlobalErrorHandler.instance = new GlobalErrorHandler();
    }
    return GlobalErrorHandler.instance;
  }

  public initialize(): void {
    if (this.isInitialized) {
      return;
    }

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection);

    // Handle global JavaScript errors
    window.addEventListener('error', this.handleGlobalError);

    // Handle chunk loading errors (common in React apps with code splitting)
    window.addEventListener('error', this.handleChunkLoadError, true);

    this.isInitialized = true;
    console.log('Global error handler initialized');
  }

  public cleanup(): void {
    if (!this.isInitialized) {
      return;
    }

    window.removeEventListener('unhandledrejection', this.handleUnhandledRejection);
    window.removeEventListener('error', this.handleGlobalError);
    window.removeEventListener('error', this.handleChunkLoadError, true);

    this.isInitialized = false;
  }

  private handleUnhandledRejection = (event: PromiseRejectionEvent): void => {
    console.error('Unhandled promise rejection:', event.reason);

    const errorDetails: ErrorDetails = {
      message: event.reason?.message || 'Unhandled promise rejection',
      stack: event.reason?.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorId: this.generateErrorId(),
      type: 'unhandledRejection'
    };

    this.reportError(errorDetails);
    this.showUserNotification('An unexpected error occurred. Please try again.');

    // Prevent the default browser error handling
    event.preventDefault();
  };

  private handleGlobalError = (event: ErrorEvent): void => {
    console.error('Global error:', event.error);

    const errorDetails: ErrorDetails = {
      message: event.message || 'Global JavaScript error',
      stack: event.error?.stack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorId: this.generateErrorId(),
      type: 'error'
    };

    this.reportError(errorDetails);
    this.showUserNotification('A JavaScript error occurred. Please refresh the page.');
  };

  private handleChunkLoadError = (event: ErrorEvent): void => {
    const target = event.target as HTMLElement;
    
    // Check if it's a chunk loading error
    if (
      target &&
      (target.tagName === 'SCRIPT' || target.tagName === 'LINK') &&
      event.message.includes('Loading chunk')
    ) {
      console.error('Chunk load error:', event);

      const errorDetails: ErrorDetails = {
        message: 'Failed to load application chunk',
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        errorId: this.generateErrorId(),
        type: 'chunkLoadError'
      };

      this.reportError(errorDetails);
      this.showChunkLoadErrorNotification();
    }
  };

  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private reportError(errorDetails: ErrorDetails): void {
    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error details:', errorDetails);
    }

    // Send to error monitoring service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoringService(errorDetails);
    }
  }

  private sendToMonitoringService(errorDetails: ErrorDetails): void {
    // Implement your error monitoring service integration here
    // Examples: Sentry, LogRocket, Bugsnag, or custom API
    
    try {
      // Example implementation:
      // fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorDetails)
      // });
      
      console.log('Would send to monitoring service:', errorDetails);
    } catch (error) {
      console.error('Failed to send error to monitoring service:', error);
    }
  }

  private showUserNotification(message: string): void {
    notification.error({
      message: 'Error',
      description: message,
      duration: 5,
      placement: 'topRight'
    });
  }

  private showChunkLoadErrorNotification(): void {
    notification.error({
      message: 'Loading Error',
      description: 'Failed to load application resources. Please refresh the page.',
      duration: 0, // Don't auto-close
      placement: 'topRight',
      btn: (
        <button
          onClick={() => window.location.reload()}
          style={{
            background: '#ff4d4f',
            color: 'white',
            border: 'none',
            padding: '4px 8px',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Refresh Page
        </button>
      )
    });
  }
}

// Export singleton instance
export const globalErrorHandler = GlobalErrorHandler.getInstance();

// Export initialization function for easy setup
export const initializeGlobalErrorHandler = (): void => {
  globalErrorHandler.initialize();
};

// Export cleanup function
export const cleanupGlobalErrorHandler = (): void => {
  globalErrorHandler.cleanup();
};

export default GlobalErrorHandler;
