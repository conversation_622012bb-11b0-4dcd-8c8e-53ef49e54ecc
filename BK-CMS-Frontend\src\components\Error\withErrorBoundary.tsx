import React, { ComponentType, ErrorInfo } from 'react';
import ErrorBoundary from './ErrorBoundary';

interface WithErrorBoundaryOptions {
  level?: 'page' | 'component' | 'critical';
  showDetails?: boolean;
  fallback?: React.ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

/**
 * Higher-order component that wraps a component with an ErrorBoundary
 * @param Component - The component to wrap
 * @param options - Configuration options for the error boundary
 * @returns Wrapped component with error boundary
 */
function withErrorBoundary<P extends object>(
  Component: ComponentType<P>,
  options: WithErrorBoundaryOptions = {}
) {
  const {
    level = 'component',
    showDetails = process.env.NODE_ENV === 'development',
    fallback,
    onError
  } = options;

  const WrappedComponent = (props: P) => {
    return (
      <ErrorBoundary
        level={level}
        showDetails={showDetails}
        fallback={fallback}
        onError={onError}
      >
        <Component {...props} />
      </ErrorBoundary>
    );
  };

  // Set display name for debugging
  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;

  return WrappedComponent;
}

export default withErrorBoundary;

// Convenience functions for different error boundary levels
export const withPageErrorBoundary = <P extends object>(
  Component: ComponentType<P>,
  options: Omit<WithErrorBoundaryOptions, 'level'> = {}
) => withErrorBoundary(Component, { ...options, level: 'page' });

export const withComponentErrorBoundary = <P extends object>(
  Component: ComponentType<P>,
  options: Omit<WithErrorBoundaryOptions, 'level'> = {}
) => withErrorBoundary(Component, { ...options, level: 'component' });

export const withCriticalErrorBoundary = <P extends object>(
  Component: ComponentType<P>,
  options: Omit<WithErrorBoundaryOptions, 'level'> = {}
) => withErrorBoundary(Component, { ...options, level: 'critical' });
