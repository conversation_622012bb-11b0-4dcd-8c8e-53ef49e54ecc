import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  Input,
  Spin,
  InputRef,
  DatePicker,
  TableColumnsType,
  Badge,
} from "antd";
import { Link } from "react-router-dom";
import dayjs, { Dayjs } from "dayjs";
import { axiosInstance } from "../../apiCalls";

import "../../assets/css/Orders/OrdersList.css";
import StoreFilterDropdown from "./OrderFilters/StoreNameFilter";
import { FilterDropdownProps } from "antd/lib/table/interface";
import { SearchOutlined } from "@ant-design/icons";
import { Order, OrdersResponse } from "../../types/OrderType/OrderType";
import { ORDERS } from "../../constants/Constant";
import { handleApiError } from "../../utils/ApiErrorHandler";
import ErrorFallback from "../Error/ErrorPage";
import { useTableFilters } from "../../hooks/useTableFilter";
import FilterMenu from "../shared/FilterMenu";
import DataTable from "../shared/DataTable/commonDataTable";
import FilterButtons from "../shared/FilterButton/FilterButton";
import useMetaData from "../../hooks/useMetaData";
import CommonPagination from "../shared/Pagination/commonPagination";

const { RangePicker } = DatePicker;

const OrdersList: React.FC = () => {
  const {
    currentPage,
    pageSize,
    filters,
    setFilters,
    appliedFilters,
    showClearButtons,
    updateURLParams,
    handlePageChange,
    handleFilterChange,
    clearFilter,
    clearAllFilters,
  } = useTableFilters();

  const { metaData, errors } = useMetaData();

  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [dateRange, setDateRange] = useState<
    [Dayjs | null, Dayjs | null] | null
  >(null);

  // const location = useLocation();
  // const navigate = useNavigate();
  const codeInputRef = useRef<InputRef>(null);

  const [searchValue, setSearchValue] = useState<string>("");

  const [dynamicAttributes, setDynamicAttributes] = useState({
    order_types: [],
    order_statuses: [],
    payment_statuses: [],
    payment_methods: [],
  });

  const memoizedFilters = useMemo(() => filters, [filters]);

  useEffect(() => {
    const orderMetaData = metaData?.order_meta;
    // console.log(orderMetaData);
    if (orderMetaData) {
      setDynamicAttributes(orderMetaData);
    }
  }, [metaData, errors]);

  useEffect(() => {
    const controller = new AbortController();
    const fetchOrders = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get<OrdersResponse>(
          "pos/orders/orders/",
          {
            params: {
              page: currentPage,
              page_size: pageSize,
              ...memoizedFilters,
            },
            signal: controller.signal,
          }
        );

        if (response.status === 200) {
          setOrders(response.data.objects);
          setTotalCount(response.data.total_count);
          setError(null);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: any) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    };

    fetchOrders();

    return () => controller.abort();
  }, [currentPage, pageSize, memoizedFilters]);

  const handleDateRangeChange = useCallback(
    (dates: [Dayjs | null, Dayjs | null] | null) => {
      if (dates) {
        const start_date = dates[0]?.format("YYYY-MM-DD");
        const end_date = dates[1]?.format("YYYY-MM-DD");
        const updatedFilters = { ...filters, start_date, end_date, page: "1" };
        setFilters(updatedFilters);
        updateURLParams(updatedFilters);
      } else {
        const resetFilters = { ...filters };
        delete resetFilters.start_date;
        delete resetFilters.end_date;
        setFilters(resetFilters);
        updateURLParams(resetFilters);
      }
    },
    [filters, setFilters, updateURLParams]
  );

  const handleSearchChange = (value: string) => {
    handleFilterChange("search_value", value);
  };

  const capitalizeFirstLetter = (text: string) => {
    if (!text) return text;
    return text.charAt(0).toUpperCase() + text.slice(1);
  };

  const clearFilterHandler = (key: string) => {
    clearFilter(key);
    if (key === "search_value") {
      setSearchValue("");
    } else if (key === "start_date" || key === "end_date") {
      setDateRange(null);
    }
  };

  const clearAllFiltersHandler = () => {
    clearAllFilters();
    setSearchValue("");
    setDateRange(null);
  };

  const formatPaymentMethod = (text: string) => {
    if (!text) return "-";

    return text
      // .replace(/[^a-zA-Z0-9 ]/g, " ")
      .replace(/[^a-zA-Z0-9\/\- ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  const handleFocus = () => {
    if (!isFocused) {
      setIsFocused(true);
      codeInputRef.current?.focus();
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  const columns: TableColumnsType<Order> = [
    {
      title: "Order ID",
      dataIndex: "id",
      key: "id",
      width: "5%",
      fixed: "left" as "left",
      render: (text: string, record: Order) =>
        text ? (
          <Link
            className="common-link text-decoration-none"
            to={`/order-details/${record.id}`}
          >
            {text}
          </Link>
        ) : (
          "N/A"
        ),
    },

    {
      title: "Customer",
      dataIndex: "customer",
      key: "customer",
      width: "15%",
      filteredValue: filters.customer ? ([filters.customer] as string[]) : null,
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: any) => (
        <div className="filter-dropdown">
          <Input
            ref={codeInputRef}
            placeholder="Search customer"
            value={selectedKeys[0]}
            onChange={(e) => {
              const value = e.target.value;
              setSelectedKeys(value ? [value] : []);
              if (!value) {
                clearFilters();
                confirm();
                handleFilterChange("customer", "All"); // Reset to show all data
              }
            }}
            onPressEnter={() => {
              confirm();
              handleFilterChange("customer", selectedKeys[0]); // Update URL
            }}
            autoFocus
            suffix={
              selectedKeys[0] ? (
                <span
                  className="search-filter-clear-btn"
                  onClick={() => {
                    setSelectedKeys([]);
                    clearFilters();
                    confirm();
                    handleFilterChange("customer", "All"); // Reset URL filter
                  }}
                >
                  ✖
                </span>
              ) : null
            }
          />
        </div>
      ),
      filterDropdownProps: {
        onOpenChange: (visible: boolean) => {
          if (visible) {
            setTimeout(() => codeInputRef.current?.focus(), 100); // Focus the input field
          }
        },
      },
      filterIcon: (filtered: boolean) => (
        <div className={`filter-icon ${filtered ? "active" : "inactive"}`}>
          <SearchOutlined />
        </div>
      ),
      render: (text: string) => (text ? <span>{text}</span> : "-"),
    },
    {
      title: "Store",
      dataIndex: "store",
      key: "store",
      width: "15%",
      filteredValue: filters.store ? ([filters.store] as string[]) : null,

      filterDropdown: useMemo(
        () =>
          ({
            setSelectedKeys,
            selectedKeys,
            confirm,
            clearFilters,
          }: FilterDropdownProps) =>
            (
              <StoreFilterDropdown
                selectedKeys={selectedKeys as string[]}
                setSelectedKeys={setSelectedKeys}
                confirm={confirm}
                clearFilters={clearFilters ?? (() => {})}
                handleFilterChange={handleFilterChange}
                inputRef={codeInputRef} // Shared ref passed
                setFilters={setFilters}
                updateURLParams={updateURLParams}
                filters={filters}
              />
            ),
        [filters.store]
      ),
      filterDropdownProps: {
        onOpenChange: (visible) => {
          if (visible) {
            setTimeout(() => codeInputRef.current?.focus(), 100);
          }
        },
      },
      filterIcon: (filtered: boolean) => (
        <div className={`filter-icon ${filtered ? "active" : "inactive"}`}>
          <SearchOutlined />
        </div>
      ),
      render: (text: string, record: Order) =>
        text ? (
          <Link
            className="common-link text-decoration-none"
            to={`/stores/${record.store_id}/details`}
          >
            {text}
          </Link>
        ) : (
          "-"
        ),
    },
    {
      title: "Order Type",
      dataIndex: "order_type",
      key: "order_type",
      width: "12%",
      filteredValue: filters.order_type ? [filters.order_type] : null,
      filterDropdown: (props) => (
        <FilterMenu
          {...props}
          filterKey="order_type"
          options={dynamicAttributes.order_types.map((status) => ({
            label: formatPaymentMethod(status),
            value: status,
          }))}
          handleFilterChange={handleFilterChange}
        />
      ),
      render: (value: string) => formatPaymentMethod(value),
    },

    {
      title: "Order Status",
      dataIndex: "order_status",
      key: "order_status",
      width: "12%",
      filteredValue: filters.order_status ? [filters.order_status] : null,
      filterDropdown: (props) => (
        <FilterMenu
          {...props}
          filterKey="order_status"
          options={dynamicAttributes.order_statuses.map((status) => ({
            label: capitalizeFirstLetter(status),
            value: status,
          }))}
          handleFilterChange={handleFilterChange}
        />
      ),
      render: (text: string) => {
        const statusClass = `status-wrapper ${text.toLowerCase()}`;

        return (
          <div className={statusClass}>
            <Badge className="status-tag">{capitalizeFirstLetter(text)}</Badge>
          </div>
        );
      },
    },

    {
      title: "Payment Status",
      dataIndex: "payment_status",
      key: "payment_status",
      width: "12%",
      filteredValue: filters.payment_status ? [filters.payment_status] : null,
      filterDropdown: (props) => (
        <FilterMenu
          {...props}
          filterKey="payment_status"
          options={dynamicAttributes.payment_statuses.map((status) => ({
            label: formatPaymentMethod(status),
            value: status,
          }))}
          handleFilterChange={handleFilterChange}
        />
      ),
      render: (status: string) => {
        const formattedClassName = String(status)
          .trim()
          .toLowerCase()
          .replace(/[\s_]+/g, "-");
        const statusClass = `status-wrapper ${formattedClassName}`;

        return (
          <div className={statusClass}>
            <Badge className="status-tag">{formatPaymentMethod(status)}</Badge>
          </div>
        );
      },
    },
    {
      title: "Date",
      dataIndex: "created_at",
      key: "created_at",
      width: "17%",
      // sorter: (a: Order, b: Order) =>
      //   dayjs(a.created_at).unix() - dayjs(b.created_at).unix(),
      render: (createdAt: string) =>
        dayjs(createdAt).format("DD MMM YYYY, hh:mm: A"), // 12-hour format  //for 24-hour format: dayjs(createdAt).format("DD MMM 'YYYY, HH:mm:ss")
    },
    {
      title: "Payment Method",
      dataIndex: "payment_method",
      key: "payment_method",
      width: "10%",
      filteredValue: filters.payment_method ? [filters.payment_method] : null,
      filterDropdown: (props) => (
        <FilterMenu
          {...props}
          filterKey="payment_method"
          options={dynamicAttributes.payment_methods.map((status) => ({
            label: formatPaymentMethod(status),
            value: status,
          }))}
          handleFilterChange={handleFilterChange}
        />
      ),
      render: (value: string) => formatPaymentMethod(value),
    },
    {
      title: "Grand Total",
      dataIndex: "grand_total",
      key: "grand_total",
      width: "10%",
      // fixed: "right" as "right",
      // sorter: (a: Order, b: Order) => a.grand_total - b.grand_total,
    },
  ];

  if (!loading && error) {
    return (
      <>
        <ErrorFallback error={error} onRetry={() => window.location.reload()} />
      </>
    );
  }

  return (
    <div className="border-radius-10">
      <div className="heading-title">{ORDERS}</div>
      <div className="d-flex justify-content-between align-items-center mb-3 mt-4 flex-wrap">
        <div className="search-btn-driver">
          <Input.Search
            ref={codeInputRef}
            placeholder="Search by Customer, ID, Mobile, or Payment ID"
            value={searchValue}
            onChange={(e) => setSearchValue(e.target.value)}
            onSearch={(value) => {
              if (value.trim()) {
                handleSearchChange(value);
              }
            }}
            onFocus={handleFocus}
            onBlur={handleBlur}
            prefix={
              <SearchOutlined
                // style={{ visibility: isFocused ? "visible" : "hidden" }}
                className={isFocused ? "" : "invisible"}
              />
            }
          />
        </div>
        <div className="date-btn-driver">
          <RangePicker
            value={dateRange}
            onChange={(dates) => {
              setDateRange(dates);
              handleDateRangeChange(dates);
            }}
            format="YYYY-MM-DD"
            getPopupContainer={(trigger: HTMLElement) =>
              trigger.closest(".date-btn-driver") || document.body
            }
            placement="topRight"
          />
        </div>
      </div>

      <FilterButtons
        showClearButtons={showClearButtons}
        appliedFilters={appliedFilters}
        clearAllFilters={clearAllFiltersHandler}
        clearFilter={clearFilterHandler}
        formatFilterValue={formatPaymentMethod}
        filters={filters}
      />

      {/* Render only actual filters, excluding `page` */}

      {loading ? (
        <div className="loading-container d-flex justify-content-center align-items-center p-5">
          <Spin size="large" />
        </div>
      ) : (
        <div className="pt-2 mt-2">
          <DataTable<Order>
            columns={columns}
            dataSource={orders}
            pagination={false}
            rowKey="id"
            scroll={{ x: "max-content" }}
          />

          <CommonPagination
            current={currentPage}
            pageSize={pageSize}
            total={totalCount}
            showSizeChanger
            onShowSizeChange={handlePageChange}
            onChange={handlePageChange}
          />
        </div>
      )}
    </div>
  );
};

export default OrdersList;
