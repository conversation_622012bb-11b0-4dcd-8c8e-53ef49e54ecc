# Error Boundary System Documentation

This project implements a comprehensive error boundary system for better error handling and user experience. The system includes multiple layers of error handling, logging, and user feedback.

## Components Overview

### 1. ErrorBoundary Component
The main error boundary component that catches JavaScript errors in the component tree.

**Features:**
- Different error levels (page, component, critical)
- Customizable error UI
- Error logging and reporting
- Development vs production modes
- Retry functionality

**Usage:**
```tsx
import ErrorBoundary from './components/Error/ErrorBoundary';

<ErrorBoundary level="page" showDetails={true}>
  <YourComponent />
</ErrorBoundary>
```

### 2. withErrorBoundary HOC
Higher-order component for wrapping components with error boundaries.

**Usage:**
```tsx
import { withComponentErrorBoundary } from './components/Error/withErrorBoundary';

const SafeComponent = withComponentErrorBoundary(YourComponent, {
  onError: (error, errorInfo) => {
    console.log('Component error:', error);
  }
});
```

### 3. useErrorHandler Hook
React hook for handling errors in functional components.

**Usage:**
```tsx
import { useErrorHandler } from './hooks/useErrorHandler';

function MyComponent() {
  const { handleError, handleAsyncError, logError } = useErrorHandler();

  const handleClick = async () => {
    const result = await handleAsyncError(async () => {
      return await fetchData();
    });
  };

  return <button onClick={handleClick}>Fetch Data</button>;
}
```

### 4. Global Error Handler
Handles unhandled promise rejections and global JavaScript errors.

**Features:**
- Unhandled promise rejection handling
- Global error catching
- Chunk loading error handling
- User notifications
- Error reporting

### 5. Error Logger
Centralized logging system for all errors.

**Features:**
- Local storage persistence
- Console logging
- Remote error reporting
- Error categorization
- Session tracking

## Error Levels

### Critical Level
- Used for application-breaking errors
- Full-screen error UI
- Reload/home navigation options
- Detailed error information in development

### Page Level
- Used for page-specific errors
- Antd Result component UI
- Retry and home navigation
- Suitable for route-level errors

### Component Level
- Used for individual component errors
- Compact error card UI
- Retry functionality
- Doesn't break the entire page

## Implementation Examples

### 1. Wrapping the Entire App
```tsx
// App.tsx
import ErrorBoundary from './components/Error/ErrorBoundary';
import { initializeGlobalErrorHandler } from './utils/globalErrorHandler';

function App() {
  useEffect(() => {
    initializeGlobalErrorHandler();
  }, []);

  return (
    <ErrorBoundary level="critical">
      <Router>
        <Routes>
          {/* Your routes */}
        </Routes>
      </Router>
    </ErrorBoundary>
  );
}
```

### 2. Wrapping Page Components
```tsx
// Layout.tsx
<Content>
  <ErrorBoundary level="page">
    <Outlet />
  </ErrorBoundary>
</Content>
```

### 3. Wrapping Individual Components
```tsx
// DataTable component
const SafeDataTable = withComponentErrorBoundary(DataTable, {
  onError: (error) => {
    errorLogger.logError('DataTable error', error, {
      component: 'DataTable',
      props: { /* relevant props */ }
    });
  }
});
```

### 4. Using Error Handler Hook
```tsx
function StoresList() {
  const { handleError, handleAsyncError } = useErrorHandler({
    context: { component: 'StoresList' }
  });

  const fetchStores = async () => {
    const stores = await handleAsyncError(async () => {
      const response = await api.getStores();
      return response.data;
    }, { action: 'fetchStores' });

    if (stores) {
      setStores(stores);
    }
  };

  return (
    <div>
      {/* Component content */}
    </div>
  );
}
```

### 5. Enhanced API Error Handling
```tsx
import { useApiErrorHandler } from './hooks/useErrorHandler';

function useStoresList() {
  const { handleApiError } = useApiErrorHandler();

  const fetchStores = async () => {
    try {
      const response = await axiosInstance.get('/stores');
      return response.data;
    } catch (error) {
      handleApiError(error, '/stores', 'GET');
      throw error;
    }
  };

  return { fetchStores };
}
```

## Configuration

### Environment Variables
```env
# Enable remote error reporting in production
REACT_APP_ERROR_ENDPOINT=https://your-error-service.com/api/errors

# Error logging configuration
REACT_APP_ENABLE_ERROR_LOGGING=true
REACT_APP_MAX_ERROR_LOGS=100
```

### Error Logger Configuration
```tsx
import { ErrorLogger } from './utils/errorLogger';

const errorLogger = ErrorLogger.getInstance({
  maxEntries: 100,
  enableConsoleLog: true,
  enableLocalStorage: true,
  enableRemoteLogging: process.env.NODE_ENV === 'production',
  remoteEndpoint: process.env.REACT_APP_ERROR_ENDPOINT
});
```

## Best Practices

1. **Use appropriate error levels:**
   - Critical: App-breaking errors
   - Page: Route/page-specific errors
   - Component: Individual component errors

2. **Provide context:**
   - Always include relevant context when logging errors
   - Include component names, user actions, and relevant data

3. **Handle async operations:**
   - Use `handleAsyncError` for promise-based operations
   - Provide meaningful error messages to users

4. **Monitor and analyze:**
   - Set up error monitoring in production
   - Regularly review error logs
   - Fix common error patterns

5. **Test error scenarios:**
   - Test error boundaries with intentional errors
   - Verify error logging and reporting
   - Ensure good user experience during errors

## Error Monitoring Integration

To integrate with error monitoring services like Sentry:

```tsx
// utils/errorReporting.ts
import * as Sentry from '@sentry/react';

export const reportError = (error: Error, context?: any) => {
  if (process.env.NODE_ENV === 'production') {
    Sentry.captureException(error, {
      extra: context
    });
  }
};

// In ErrorBoundary component
componentDidCatch(error: Error, errorInfo: ErrorInfo) {
  reportError(error, {
    componentStack: errorInfo.componentStack,
    errorBoundary: this.constructor.name
  });
}
```

This error boundary system provides comprehensive error handling that improves both developer experience and user experience by gracefully handling errors and providing useful debugging information.
