import { Component, ErrorInfo, ReactNode } from "react";
import { Button, Typography, Result, Space, Collapse, Card } from "antd";
import {
  ReloadOutlined,
  HomeOutlined,
  BugOutlined,
  WarningOutlined
} from "@ant-design/icons";

const { Title, Paragraph, Text } = Typography;
const { Panel } = Collapse;

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  level?: 'page' | 'component' | 'critical';
  showDetails?: boolean;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    return {
      hasError: true,
      error,
      errorId
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    this.setState({ errorInfo });

    // Log error details
    const errorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errorId: this.state.errorId
    };

    console.error("Error caught by ErrorBoundary:", errorDetails);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Send error to monitoring service (implement as needed)
    this.reportError(errorDetails);
  }

  private reportError = (errorDetails: any) => {
    // Implement error reporting to your monitoring service
    // Example: Sentry, LogRocket, or custom API
    if (process.env.NODE_ENV === 'production') {
      // Send to error monitoring service
      console.log('Reporting error to monitoring service:', errorDetails);
    }
  };

  handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  handleGoHome = () => {
    window.location.href = '/orders-list';
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { level = 'page', showDetails = false } = this.props;
      const { error, errorInfo, errorId } = this.state;

      // Different UI based on error level
      if (level === 'component') {
        return (
          <Card
            style={{ margin: '16px 0' }}
            styles={{ body: { textAlign: 'center', padding: '24px' } }}
          >
            <WarningOutlined style={{ fontSize: '48px', color: '#faad14', marginBottom: '16px' }} />
            <Title level={4}>Component Error</Title>
            <Paragraph type="secondary">
              This component encountered an error and couldn't render properly.
            </Paragraph>
            <Button type="primary" onClick={this.handleRetry} icon={<ReloadOutlined />}>
              Retry
            </Button>
          </Card>
        );
      }

      if (level === 'critical') {
        return (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              height: "100vh",
              textAlign: "center",
              padding: "20px",
              backgroundColor: "#f5f5f5"
            }}
          >
            <BugOutlined style={{ fontSize: '72px', color: '#ff4d4f', marginBottom: '24px' }} />
            <Title level={1} style={{ color: '#ff4d4f' }}>Critical Error</Title>
            <Paragraph style={{ fontSize: '16px', maxWidth: '600px' }}>
              A critical error has occurred that prevents the application from functioning properly.
              Please refresh the page or contact support if the problem persists.
            </Paragraph>
            <Space size="large" style={{ marginTop: '24px' }}>
              <Button
                type="primary"
                size="large"
                onClick={this.handleReload}
                icon={<ReloadOutlined />}
              >
                Reload Application
              </Button>
              <Button
                size="large"
                onClick={this.handleGoHome}
                icon={<HomeOutlined />}
              >
                Go to Home
              </Button>
            </Space>
            {showDetails && error && (
              <div style={{ marginTop: '32px', width: '100%', maxWidth: '800px' }}>
                <Collapse>
                  <Panel header="Error Details" key="1">
                    <div style={{ textAlign: 'left' }}>
                      <Text strong>Error ID: </Text>
                      <Text code>{errorId}</Text>
                      <br />
                      <Text strong>Message: </Text>
                      <Text>{error.message}</Text>
                      <br />
                      <Text strong>Stack Trace:</Text>
                      <pre style={{
                        background: '#f6f8fa',
                        padding: '12px',
                        borderRadius: '6px',
                        fontSize: '12px',
                        overflow: 'auto',
                        maxHeight: '200px'
                      }}>
                        {error.stack}
                      </pre>
                      {errorInfo && (
                        <>
                          <Text strong>Component Stack:</Text>
                          <pre style={{
                            background: '#f6f8fa',
                            padding: '12px',
                            borderRadius: '6px',
                            fontSize: '12px',
                            overflow: 'auto',
                            maxHeight: '200px'
                          }}>
                            {errorInfo.componentStack}
                          </pre>
                        </>
                      )}
                    </div>
                  </Panel>
                </Collapse>
              </div>
            )}
          </div>
        );
      }

      // Default page-level error UI
      return (
        <Result
          status="error"
          title="Something went wrong"
          subTitle={error?.message || "An unexpected error occurred while loading this page."}
          extra={[
            <Button
              type="primary"
              key="retry"
              onClick={this.handleRetry}
              icon={<ReloadOutlined />}
            >
              Try Again
            </Button>,
            <Button
              key="home"
              onClick={this.handleGoHome}
              icon={<HomeOutlined />}
            >
              Go Home
            </Button>,
          ]}
        >
          {showDetails && error && (
            <div style={{ marginTop: '24px' }}>
              <Collapse>
                <Panel header="Technical Details" key="1">
                  <div style={{ textAlign: 'left' }}>
                    <Text strong>Error ID: </Text>
                    <Text code>{errorId}</Text>
                    <br />
                    <Text strong>Timestamp: </Text>
                    <Text>{new Date().toLocaleString()}</Text>
                    <br />
                    <Text strong>Stack Trace:</Text>
                    <pre style={{
                      background: '#f6f8fa',
                      padding: '12px',
                      borderRadius: '6px',
                      fontSize: '12px',
                      overflow: 'auto',
                      maxHeight: '150px'
                    }}>
                      {error.stack}
                    </pre>
                  </div>
                </Panel>
              </Collapse>
            </div>
          )}
        </Result>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
