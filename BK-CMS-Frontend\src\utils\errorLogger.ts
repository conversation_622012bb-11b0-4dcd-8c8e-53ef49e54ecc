interface ErrorLogEntry {
  id: string;
  timestamp: string;
  level: 'error' | 'warning' | 'info';
  message: string;
  stack?: string;
  context?: Record<string, any>;
  userAgent: string;
  url: string;
  userId?: string;
  sessionId?: string;
}

interface ErrorLoggerConfig {
  maxEntries: number;
  enableConsoleLog: boolean;
  enableLocalStorage: boolean;
  enableRemoteLogging: boolean;
  remoteEndpoint?: string;
}

class ErrorLogger {
  private static instance: ErrorLogger;
  private config: ErrorLoggerConfig;
  private logs: ErrorLogEntry[] = [];
  private sessionId: string;

  private constructor(config: Partial<ErrorLoggerConfig> = {}) {
    this.config = {
      maxEntries: 100,
      enableConsoleLog: true,
      enableLocalStorage: true,
      enableRemoteLogging: false,
      ...config
    };
    
    this.sessionId = this.generateSessionId();
    this.loadLogsFromStorage();
  }

  public static getInstance(config?: Partial<ErrorLoggerConfig>): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger(config);
    }
    return ErrorLogger.instance;
  }

  public logError(
    message: string,
    error?: Error,
    context?: Record<string, any>
  ): void {
    this.log('error', message, error?.stack, context);
  }

  public logWarning(
    message: string,
    context?: Record<string, any>
  ): void {
    this.log('warning', message, undefined, context);
  }

  public logInfo(
    message: string,
    context?: Record<string, any>
  ): void {
    this.log('info', message, undefined, context);
  }

  private log(
    level: 'error' | 'warning' | 'info',
    message: string,
    stack?: string,
    context?: Record<string, any>
  ): void {
    const entry: ErrorLogEntry = {
      id: this.generateId(),
      timestamp: new Date().toISOString(),
      level,
      message,
      stack,
      context,
      userAgent: navigator.userAgent,
      url: window.location.href,
      sessionId: this.sessionId,
      userId: this.getCurrentUserId()
    };

    this.addLogEntry(entry);

    if (this.config.enableConsoleLog) {
      this.logToConsole(entry);
    }

    if (this.config.enableLocalStorage) {
      this.saveLogsToStorage();
    }

    if (this.config.enableRemoteLogging) {
      this.sendToRemote(entry);
    }
  }

  private addLogEntry(entry: ErrorLogEntry): void {
    this.logs.push(entry);

    // Keep only the most recent entries
    if (this.logs.length > this.config.maxEntries) {
      this.logs = this.logs.slice(-this.config.maxEntries);
    }
  }

  private logToConsole(entry: ErrorLogEntry): void {
    const logMethod = entry.level === 'error' ? console.error :
                     entry.level === 'warning' ? console.warn :
                     console.info;

    logMethod(`[${entry.level.toUpperCase()}] ${entry.message}`, {
      id: entry.id,
      timestamp: entry.timestamp,
      stack: entry.stack,
      context: entry.context,
      url: entry.url
    });
  }

  private saveLogsToStorage(): void {
    try {
      localStorage.setItem('errorLogs', JSON.stringify(this.logs));
    } catch (error) {
      console.warn('Failed to save logs to localStorage:', error);
    }
  }

  private loadLogsFromStorage(): void {
    try {
      const stored = localStorage.getItem('errorLogs');
      if (stored) {
        this.logs = JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Failed to load logs from localStorage:', error);
      this.logs = [];
    }
  }

  private sendToRemote(entry: ErrorLogEntry): void {
    if (!this.config.remoteEndpoint) {
      return;
    }

    // Send asynchronously without blocking the main thread
    setTimeout(() => {
      fetch(this.config.remoteEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(entry)
      }).catch(error => {
        console.warn('Failed to send log to remote endpoint:', error);
      });
    }, 0);
  }

  private generateId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private getCurrentUserId(): string | undefined {
    // Implement your user ID retrieval logic here
    // For example, from cookies, localStorage, or context
    try {
      // Example: return Cookies.get('userId');
      return undefined;
    } catch {
      return undefined;
    }
  }

  public getLogs(level?: 'error' | 'warning' | 'info'): ErrorLogEntry[] {
    if (level) {
      return this.logs.filter(log => log.level === level);
    }
    return [...this.logs];
  }

  public clearLogs(): void {
    this.logs = [];
    if (this.config.enableLocalStorage) {
      localStorage.removeItem('errorLogs');
    }
  }

  public exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  public getLogsSummary(): { total: number; errors: number; warnings: number; info: number } {
    return {
      total: this.logs.length,
      errors: this.logs.filter(log => log.level === 'error').length,
      warnings: this.logs.filter(log => log.level === 'warning').length,
      info: this.logs.filter(log => log.level === 'info').length
    };
  }
}

// Export singleton instance with default config
export const errorLogger = ErrorLogger.getInstance({
  enableRemoteLogging: process.env.NODE_ENV === 'production',
  remoteEndpoint: process.env.REACT_APP_ERROR_ENDPOINT
});

export default ErrorLogger;
