import React, { useState, useCallback } from "react";
import { Form, Input, <PERSON><PERSON>, Card, message } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";
import ErrorFallback from "../../Error/ErrorPage";

import { handleApiError } from "../../../utils/ApiErrorHandler";
import { ReleaseNoteType } from "../releaseNoteType";

const AddReleaseNote: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = useCallback(
    async (values: ReleaseNoteType) => {
      try {
        setLoading(true);
        const response = await axiosInstance.post(
          "/cms/stores/release-note/",
          values
        );

        if (response.status === 201 && response.data.id) {
          message.success(response.data.message);
          navigate("/release-notes-list");
        }
      } catch (error) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    },
    [navigate]
  );

  if (error) {
    return (
      <ErrorFallback error={error} onRetry={() => window.location.reload()} />
    );
  }

  return (
    <div>
      <h2 className="heading-title">Add Release Note</h2>
      <Card className="mt-3">
        <Form
          form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 14 }}
          style={{ maxWidth: 600 }}
          onFinish={handleSubmit}
          initialValues={{ name: "", code: "", description: "" }}
        >
          <Form.Item
            label="Name"
            name="name"
            rules={[{ required: true, message: "Please enter the name" }]}
          >
            <Input placeholder="Enter name" />
          </Form.Item>

          <Form.Item
            label="Code"
            name="code"
            rules={[{ required: true, message: "Please enter the code" }]}
          >
            <Input placeholder="Enter code" />
          </Form.Item>

          <Form.Item
            label="Description"
            name="description"
            rules={[
              { required: true, message: "Please enter the description" },
            ]}
          >
            <Input.TextArea placeholder="Enter description" rows={4} />
          </Form.Item>

          <Form.Item label={null}>
            <Button
              type="primary"
              htmlType="submit"
              disabled={loading}
              className="btn-save"
            >
              {loading ? <LoadingOutlined spin /> : "Save"}
            </Button>
            <Button
              type="default"
              className="btn-cancel"
              onClick={() => navigate(-1)}
              style={{ marginLeft: 8 }}
            >
              Cancel
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default AddReleaseNote;
