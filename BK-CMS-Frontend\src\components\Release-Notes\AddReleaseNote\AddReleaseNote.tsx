import React, { useState, useCallback, useEffect } from "react";
import { Form, Input, Button, Card, message, Select, Checkbox } from "antd";
import { LoadingOutlined } from "@ant-design/icons";
import { useNavigate } from "react-router-dom";
import { axiosInstance } from "../../../apiCalls";
import ErrorFallback from "../../Error/ErrorPage";
import { ReleaseNoteType } from "../releaseNoteType";
import useStores from "../../../hooks/useStores";
import debounce from "lodash.debounce";

const AddReleaseNote: React.FC = () => {
  const {
    stores,
    loading: storesLoading,
    error,
    fetchStores,
    page,
    hasMore,
  } = useStores();

  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  const [selectedStoreIds, setSelectedStoreIds] = useState<number[]>([]);

  useEffect(() => {
    fetchStores();
  }, []);

  const handleStoreSearch = useCallback(
    debounce((value: string) => {
      fetchStores(value);
    }, 300),
    []
  );

  const handleSelectChange = (values: number[]) => {
    setSelectedStoreIds(values);
    if (values.length !== stores.length) setSelectAll(false);
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    // if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
    //   if (hasMore && !storesLoading) {
    //     fetchStores("", page + 1);
    //   }
    // }
    if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
      if (hasMore && !storesLoading) {
        fetchStores("", page + 1);
      }
    }
  };

  const handleSubmit = useCallback(
    async (values: ReleaseNoteType) => {
      const storePayload = selectAll
        ? { list: [], type: "all" }
        : {
            list: stores
              .filter((store) => selectedStoreIds.includes(store.id))
              .map((store) => ({
                id: store.id,
                third_party_id: store.third_party_id,
              })),
            type: "selected",
          };

      const payload = {
        name: values.name,
        description: values.description,
        notify_type: values.notify_type,
        stores: storePayload,
      };

      try {
        setLoading(true);
        const response = await axiosInstance.post(
          "/cms/stores/release-note/",
          payload
        );
        if (response.status === 201) {
          message.success(response.data.message);
          navigate("/release-notes-list");
        }
      } catch (error) {
        // handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    },
    [navigate, selectAll, selectedStoreIds, stores]
  );

  if (error) {
    return (
      <ErrorFallback error={error} onRetry={() => window.location.reload()} />
    );
  }

  return (
    <div>
      <h2 className="heading-title">Add Release Note</h2>
      <Card className="mt-3">
        <Form
          form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 14 }}
          style={{ maxWidth: 600 }}
          onFinish={handleSubmit}
          initialValues={{ name: "", description: "", notify_type: "" }}
        >
          <Form.Item
            label="Name"
            name="name"
            rules={[{ required: true, message: "Please enter the name" }]}
          >
            <Input placeholder="Enter name" />
          </Form.Item>

          <Form.Item
            label="Description"
            name="description"
            rules={[
              { required: true, message: "Please enter the description" },
            ]}
          >
            <Input.TextArea placeholder="Enter description" rows={4} />
          </Form.Item>

          <Form.Item
            label="Notify Type"
            name="notify_type"
            rules={[{ required: true, message: "Please select notify type" }]}
          >
            <Select placeholder="Select notify type">
              <Select.Option value="menu">Menu</Select.Option>
              <Select.Option value="config">Config</Select.Option>
              <Select.Option value="ui">UI</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item label="Select Stores">
            <Checkbox
              checked={selectAll}
              onChange={(e) => {
                const isChecked = e.target.checked;
                setSelectAll(isChecked);
                setSelectedStoreIds(isChecked ? stores.map((s) => s.id) : []);
              }}
              style={{ marginBottom: 8 }}
            >
              Select All Stores
            </Checkbox>
            <Select
             className="scrollable-select"
              mode="multiple"
              //disabled={selectAll}
              style={{ width: "100%" }}
              placeholder="Search and select stores"
              value={selectedStoreIds}
              onChange={handleSelectChange}
              onSearch={handleStoreSearch}
              onPopupScroll={handleScroll}
              showSearch
              filterOption={false}
              loading={storesLoading}
            >
              {stores.map((store) => (
                <Select.Option key={store.id} value={store.id}>
                  <Checkbox
                    checked={selectedStoreIds.includes(store.id)}
                    style={{ pointerEvents: "none" }}
                  >
                    {store.name || `Store ${store.id}`}
                  </Checkbox>
                </Select.Option>
              ))}
              {storesLoading && hasMore && (
                <Select.Option disabled key="loading" value="loading">
                  <div className="d-flex justify-content-center align-items-center p-2">
                    <LoadingOutlined spin className="font-size-16" />
                  </div>
                </Select.Option>
              )}
            </Select>
          </Form.Item>

          <Form.Item label={null}>
            <Button
              type="primary"
              htmlType="submit"
              disabled={loading}
              className="btn-save"
            >
              {loading ? <LoadingOutlined spin /> : "Save"}
            </Button>
            <Button
              type="default"
              className="btn-cancel"
              onClick={() => navigate(-1)}
              style={{ marginLeft: 8 }}
            >
              Cancel
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default AddReleaseNote;
