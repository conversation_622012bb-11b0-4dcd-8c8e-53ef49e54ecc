import React, { useState } from 'react';
import { Button, Space, Typography } from 'antd';
import { BugOutlined } from '@ant-design/icons';

const { Text } = Typography;

interface ErrorTestProps {
  shouldThrow?: boolean;
}

/**
 * Simple component for testing error boundaries
 * This component will throw an error when shouldThrow is true
 */
const ErrorTest: React.FC<ErrorTestProps> = ({ shouldThrow = false }) => {
  const [count, setCount] = useState(0);

  // This will throw an error if shouldThrow is true
  if (shouldThrow) {
    throw new Error(`Test error thrown at count: ${count}`);
  }

  return (
    <Space direction="vertical" align="center">
      <Text>Error Test Component - Count: {count}</Text>
      <Button 
        type="primary" 
        onClick={() => setCount(count + 1)}
        icon={<BugOutlined />}
      >
        Increment Count
      </Button>
      <Text type="secondary">
        This component is working normally. Use the parent controls to trigger an error.
      </Text>
    </Space>
  );
};

export default ErrorTest;
