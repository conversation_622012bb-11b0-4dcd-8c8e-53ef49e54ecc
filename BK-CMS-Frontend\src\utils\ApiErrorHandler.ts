import axios from "axios";
import { errorLogger } from "./errorLogger";


const DEBUG = import.meta.env.MODE === "development";
// Enable logs only in development

/**
 * Enhanced API error handler with logging and better error categorization
 * @param error - The error object from the API call.
 * @param setError - Function to update the error state.
 * @param context - Additional context for error logging
 */
export const handleApiError = (
  error: unknown,
  setError: (message: string | null) => void,
  context?: Record<string, any>
) => {
  // Handle request cancellation
  if (axios.isCancel(error)) {
    DEBUG && console.warn("Request canceled:", (error as any).message);
    errorLogger.logInfo("API request canceled", {
      message: (error as any).message,
      ...context
    });
    return;
  }

  // Handle network connectivity issues
  if (!navigator.onLine) {
    const message = "No internet connection. Please check your network.";
    setError(message);
    errorLogger.logError("Network connectivity error", new Error(message), {
      type: "network",
      online: navigator.onLine,
      ...context
    });
    return;
  }

  // Handle Axios errors with response
  if (axios.isAxiosError(error) && error.response) {
    const { status, data, config } = error.response;

    const errorMessage =
      {
        400: "Bad request. Please check your input and try again.",
        401: "Unauthorized: Invalid credentials or session expired.",
        403: "Forbidden. You do not have permission to access this page.",
        404: "The requested resource was not found.",
        409: "Conflict. The resource already exists or there's a conflict.",
        422: "Validation error. Please check your input.",
        429: "Too many requests. Please try again later.",
        500: "A server error occurred. Please try again later.",
        502: "Bad gateway. The server is temporarily unavailable.",
        503: "Service unavailable. Please try again later.",
        504: "Gateway timeout. The request took too long to process.",
      }[status] ||
      data?.message ||
      data?.error ||
      `API Error: ${status}`;

    setError(errorMessage);

    errorLogger.logError(`API Error: ${status}`, error, {
      status,
      url: config?.url,
      method: config?.method?.toUpperCase(),
      responseData: data,
      ...context
    });

    DEBUG && console.error(`API Error [${status}]:`, {
      url: config?.url,
      method: config?.method,
      data,
      error
    });
    return;
  }

  // Handle network errors (no response received)
  if (axios.isAxiosError(error) && error.request) {
    const message = "Network error. Unable to reach the server. Please try again.";
    setError(message);

    errorLogger.logError("Network error", error, {
      type: "network",
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      timeout: error.code === 'ECONNABORTED',
      ...context
    });

    DEBUG && console.error("Network Error:", error.request);
    return;
  }

  // Handle any other unknown errors
  const message = (error as any)?.message || "An unexpected error occurred.";
  setError(message);

  errorLogger.logError("Unknown API error", error as Error, {
    type: "unknown",
    ...context
  });

  DEBUG && console.error("Unknown Error:", error);
};

/**
 * Enhanced API error handler that returns error details instead of setting state
 * Useful for components that need more control over error handling
 */
export const getApiErrorDetails = (error: unknown, context?: Record<string, any>) => {
  let errorDetails = {
    message: "An unexpected error occurred.",
    status: null as number | null,
    type: "unknown" as "canceled" | "network" | "api" | "unknown",
    data: null as any
  };

  if (axios.isCancel(error)) {
    errorDetails = {
      message: "Request was canceled",
      status: null,
      type: "canceled",
      data: null
    };
  } else if (!navigator.onLine) {
    errorDetails = {
      message: "No internet connection. Please check your network.",
      status: null,
      type: "network",
      data: null
    };
  } else if (axios.isAxiosError(error) && error.response) {
    const { status, data } = error.response;
    errorDetails = {
      message: data?.message || data?.error || `API Error: ${status}`,
      status,
      type: "api",
      data
    };
  } else if (axios.isAxiosError(error) && error.request) {
    errorDetails = {
      message: "Network error. Unable to reach the server.",
      status: null,
      type: "network",
      data: null
    };
  } else {
    errorDetails = {
      message: (error as any)?.message || "An unexpected error occurred.",
      status: null,
      type: "unknown",
      data: null
    };
  }

  // Log the error
  errorLogger.logError(`API Error: ${errorDetails.type}`, error as Error, {
    ...errorDetails,
    ...context
  });

  return errorDetails;
};
