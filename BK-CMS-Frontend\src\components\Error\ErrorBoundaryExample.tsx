import React, { useState } from 'react';
import { <PERSON>ton, Card, Space, Typography, Divider } from 'antd';
import { BugOutlined, WarningOutlined, InfoCircleOutlined } from '@ant-design/icons';
import ErrorBoundary from './ErrorBoundary';
import { withComponentErrorBoundary } from './withErrorBoundary';
import { useErrorHandler } from '../../hooks/useErrorHandler';

const { Title, Paragraph, Text } = Typography;

// Component that throws an error
const BuggyComponent: React.FC<{ shouldThrow: boolean }> = ({ shouldThrow }) => {
  if (shouldThrow) {
    throw new Error('This is a test error from BuggyComponent!');
  }
  return <Text type="success">Component rendered successfully!</Text>;
};

// Component wrapped with error boundary HOC
const SafeBuggyComponent = withComponentErrorBoundary(BuggyComponent, {
  onError: (error, errorInfo) => {
    console.log('Error caught by HOC:', error.message);
  }
});

// Component demonstrating useErrorHandler hook
const ErrorHandlerDemo: React.FC = () => {
  const { handleError, handleAsyncError, logError, logWarning, logInfo } = useErrorHandler({
    context: { component: 'ErrorHandlerDemo' }
  });

  const handleSyncError = () => {
    handleError(new Error('This is a synchronous error'));
  };

  const handleAsyncErrorDemo = async () => {
    const result = await handleAsyncError(async () => {
      // Simulate an async operation that fails
      await new Promise(resolve => setTimeout(resolve, 1000));
      throw new Error('This is an async error');
    });
    
    if (result) {
      console.log('Async operation succeeded:', result);
    }
  };

  const handleLogging = () => {
    logError('This is an error log', new Error('Sample error'));
    logWarning('This is a warning log');
    logInfo('This is an info log');
  };

  return (
    <Card title="Error Handler Hook Demo" style={{ marginBottom: 16 }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Button onClick={handleSyncError} icon={<BugOutlined />}>
          Trigger Sync Error
        </Button>
        <Button onClick={handleAsyncErrorDemo} icon={<WarningOutlined />}>
          Trigger Async Error
        </Button>
        <Button onClick={handleLogging} icon={<InfoCircleOutlined />}>
          Test Logging
        </Button>
      </Space>
    </Card>
  );
};

// Main example component
const ErrorBoundaryExample: React.FC = () => {
  const [throwError, setThrowError] = useState(false);
  const [throwHOCError, setThrowHOCError] = useState(false);

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>Error Boundary System Examples</Title>
      <Paragraph>
        This page demonstrates the error boundary system implementation. 
        Use the buttons below to test different error scenarios.
      </Paragraph>

      <Divider />

      {/* Example 1: Basic Error Boundary */}
      <Card title="1. Basic Error Boundary" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Paragraph>
            This example shows a component wrapped in an error boundary. 
            When an error occurs, the error boundary catches it and displays a fallback UI.
          </Paragraph>
          
          <ErrorBoundary level="component">
            <BuggyComponent shouldThrow={throwError} />
          </ErrorBoundary>
          
          <Button 
            type="primary" 
            danger={throwError}
            onClick={() => setThrowError(!throwError)}
          >
            {throwError ? 'Fix Component' : 'Break Component'}
          </Button>
        </Space>
      </Card>

      {/* Example 2: HOC Error Boundary */}
      <Card title="2. Higher-Order Component Error Boundary" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Paragraph>
            This example shows a component wrapped using the withErrorBoundary HOC.
            The HOC provides a convenient way to add error boundaries to components.
          </Paragraph>
          
          <SafeBuggyComponent shouldThrow={throwHOCError} />
          
          <Button 
            type="primary" 
            danger={throwHOCError}
            onClick={() => setThrowHOCError(!throwHOCError)}
          >
            {throwHOCError ? 'Fix HOC Component' : 'Break HOC Component'}
          </Button>
        </Space>
      </Card>

      {/* Example 3: Error Handler Hook */}
      <ErrorHandlerDemo />

      {/* Example 4: Different Error Levels */}
      <Card title="4. Different Error Boundary Levels" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Paragraph>
            Error boundaries can be configured with different levels:
          </Paragraph>
          
          <ul>
            <li><strong>Critical:</strong> Full-screen error with reload options</li>
            <li><strong>Page:</strong> Page-level error with navigation options</li>
            <li><strong>Component:</strong> Component-level error with retry option</li>
          </ul>
          
          <Text type="secondary">
            The examples above use component-level error boundaries. 
            Page and critical levels are used at higher levels in the application.
          </Text>
        </Space>
      </Card>

      {/* Example 5: Error Logging */}
      <Card title="5. Error Logging" style={{ marginBottom: 16 }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Paragraph>
            All errors are automatically logged with context information:
          </Paragraph>
          
          <ul>
            <li>Error message and stack trace</li>
            <li>Component information</li>
            <li>User session data</li>
            <li>Browser and environment details</li>
            <li>Custom context data</li>
          </ul>
          
          <Text type="secondary">
            Check the browser console and localStorage to see logged errors.
            In production, errors would be sent to a monitoring service.
          </Text>
        </Space>
      </Card>

      <Divider />

      <Card>
        <Title level={4}>Implementation Notes</Title>
        <ul>
          <li>Error boundaries only catch errors in React components during rendering, lifecycle methods, and constructors</li>
          <li>They do NOT catch errors in event handlers, async code, or errors thrown during server-side rendering</li>
          <li>Use the useErrorHandler hook for handling errors in event handlers and async operations</li>
          <li>The global error handler catches unhandled promise rejections and global JavaScript errors</li>
          <li>All errors are logged with context for debugging and monitoring</li>
        </ul>
      </Card>
    </div>
  );
};

export default ErrorBoundaryExample;
