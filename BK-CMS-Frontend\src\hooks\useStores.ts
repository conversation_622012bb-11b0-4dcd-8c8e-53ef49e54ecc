// hooks/useStores.ts
import { useState } from "react";
import { handleApiError } from "../utils/ApiErrorHandler";
import { axiosInstance } from "../apiCalls";
import { Store } from "../types";

const useStores = () => {
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const [hasMore, setHasMore] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);

  const fetchStores = async (searchText: string = "", pageNumber = 1) => {
    setLoading(true);
    try {
      const params: any = {
        page: pageNumber,
        page_size: 999,
      };
      if (searchText) {
        params.name = searchText;
      }
      const response = await axiosInstance.get("/cms/stores/stores/", {
        params,
      });
      if (response.status === 200) {
        const newStores = response.data?.objects || [];

        if (pageNumber === 1) {
          setStores(newStores);
        } else {
          setStores((prev) => [...prev, ...newStores]);
        }

        setHasMore(newStores.length > 0);
        setPage(pageNumber);
      } else {
        setError("Failed to fetch stores");
      }
    } catch (err: unknown) {
      handleApiError(err, setError);
    } finally {
      setLoading(false);
    }
  };

  return { stores, loading, error, fetchStores, hasMore, page, setPage };
};

export default useStores;
