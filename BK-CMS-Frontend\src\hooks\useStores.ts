import { useEffect, useMemo, useRef, useState } from "react";
import { Store } from "../../types";
import { axiosInstance } from "../../apiCalls";
import { Link, useNavigate } from "react-router-dom";
import { Button, InputRef, Input, Alert } from "antd";
import type { TableColumnsType } from "antd";
import { Add_New_Store, Retry, Store_List } from "../../constants/Constant";

import "./StoresList.css";
import { kioskIs_Active_Options } from "../Store-mapping/Kiosk/SelectTypeKiosk/SelectTypeKioksList";
import dayjs from "dayjs";
import { SearchOutlined } from "@ant-design/icons";
import { FilterDropdownProps } from "antd/lib/table/interface";
import { handleApiError } from "../../utils/ApiErrorHandler";
import FilterButtons from "../shared/FilterButton/FilterButton";
import DataTable from "../shared/DataTable/commonDataTable";
import { useTableFilters } from "../../hooks/useTableFilter";
import FilterMenu from "../shared/FilterMenu";
import CommonPagination from "../shared/Pagination/commonPagination";

interface StoresResponse {
  objects: Store[];
  total_count: number;
}

interface StatusOption {
  label: string;
  value: string | number;
}

const StoresList = () => {
  const {
    currentPage,
    pageSize,
    filters,
    appliedFilters,
    showClearButtons,
    handlePageChange,
    handleFilterChange,
    clearFilter,
    clearAllFilters,
  } = useTableFilters();
  const [stores, setStores] = useState<Store[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const navigate = useNavigate();

  const codeInputRef = useRef<InputRef>(null);

  const [totalCount, setTotalCount] = useState<number>(0);

  const memoizedFilters = useMemo(() => filters, [filters]);

  useEffect(() => {
    const controller = new AbortController();
    const fetchStores = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await axiosInstance.get<StoresResponse>(
          "/cms/stores/stores/",
          {
            params: {
              page: currentPage,
              page_size: pageSize,
              ...memoizedFilters,
            },
            signal: controller.signal,
          }
        );

        if (response.status === 200) {
          setStores(response.data.objects);
          setTotalCount(response.data.total_count);
          setError(null);
        } else {
          setError("Unexpected response format.");
        }
      } catch (error: unknown) {
        handleApiError(error, setError);
      } finally {
        setLoading(false);
      }
    };

    fetchStores();
    return () => controller.abort();
  }, [currentPage, pageSize, memoizedFilters]);

  // const fetchStores = async (
  //   page: number,
  //   size: number,
  //   filters: Record<string, string | undefined>
  // ) => {
  //   try {
  //     setLoading(true);
  //     setError(null);

  //     const params = { page, page_size: size, ...filters };
  //     const response = await axiosInstance.get("/cms/stores/stores/", {
  //       params: params,
  //     });
  //     if (response.status === 200) {
  //       setStores(response.data.objects); // Update table data
  //       setTotalCount(response.data.total_count); // Update pagination count
  //     } else {
  //       setError("Failed to fetch Stores List");
  //     }
  //   } catch (error: any) {
  //     setError(error.response?.data?.message || "Failed to fetch Stores List");
  //   } finally {
  //     setLoading(false);
  //   }
  // };

  // useEffect(() => {
  //   fetchStores(currentPage, pageSize, filters);
  // }, [currentPage, pageSize, filters]);

  // // Remove a single filter and update the URL
  // const clearFilter = (key: string) => {
  //   const updatedFilters = { ...filters };
  //   delete updatedFilters[key];

  //   setFilters(updatedFilters);

  //   // Update URL
  //   const params = new URLSearchParams();
  //   Object.entries(updatedFilters).forEach(([k, v]) => {
  //     if (v) params.set(k, v);
  //   });

  //   navigate(params.toString() ? `?${params.toString()}` : location.pathname);
  // };

  // // Clear all filters and reset URL
  // const clearAllFilters = () => {
  //   setFilters({});

  //   navigate(location.pathname); // Reset URL
  // };

  // text formatter
  // const TextFormat = (text: string) => {
  //   if (!text) return "-";
  //   return text
  //     .replace(/[^a-zA-Z0-9 ]/g, " ")
  //     .trim()
  //     .split(" ")
  //     .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
  //     .join(" ");
  // };

  // const getFilterMenu = (
  //   setSelectedKeys: (keys: string[]) => void,
  //   selectedKeys: React.Key[], // Using React.Key[] as it includes both string and number
  //   confirm: () => void,
  //   filterKey: string,
  //   options: { label: string; value: string | number }[]
  // ) => {
  //   return (
  //     <Menu
  //       onClick={({ key }) => {
  //         const stringKey = String(key);
  //         setSelectedKeys(stringKey ? [stringKey] : []);
  //         confirm();
  //         handleFilterChange(filterKey, stringKey);
  //       }}
  //       selectedKeys={selectedKeys.map((key) => String(key))}
  //       items={[
  //         { key: "All", label: "All" },
  //         ...options
  //           .filter((option) => option.value !== "All")
  //           .map((option) => ({
  //             key: String(option.value),
  //             label: option.label,
  //           })),
  //       ]}
  //     />
  //   );
  // };

  const columns: TableColumnsType<Store> = [
    {
      title: "Code",
      dataIndex: "code",
      key: "code",
      width: "10%",
      fixed: "left" as "left",
      filteredValue: filters.code ? ([filters.code] as string[]) : null,
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: any) => (
        <div className="filter-dropdown">
          <Input
            className="filter-input-code"
            ref={codeInputRef}
            placeholder="Search code"
            value={selectedKeys[0]}
            onChange={(e) => {
              const value = e.target.value;
              setSelectedKeys(value ? [value] : []);
              if (!value) {
                clearFilters();
                confirm();
                handleFilterChange("code", "All"); // Reset to show all data
              }
            }}
            onPressEnter={() => {
              confirm();
              handleFilterChange("code", selectedKeys[0]); // Update URL
            }}
            autoFocus
            suffix={
              selectedKeys[0] ? (
                <span
                  onClick={() => {
                    setSelectedKeys([]);
                    clearFilters();
                    confirm();
                    handleFilterChange("code", "All"); // Reset URL filter
                  }}
                  className="search-filter-clear-btn"
                >
                  ✖
                </span>
              ) : null
            }
          />
        </div>
      ),
      filterDropdownProps: {
        onOpenChange: (visible: boolean) => {
          if (visible) {
            setTimeout(() => codeInputRef.current?.focus(), 100); // Focus the input field
          }
        },
      },
      render: (text: string, record: Store) => (
        <Link
          className="common-link text-decoration-none"
          to={`/stores/${record.id}/details`}
        >
          {text}
        </Link>
      ),
      filterIcon: (filtered: boolean) => (
        <div className={`filter-icon ${filtered ? "active" : "inactive"}`}>
          <SearchOutlined />
        </div>
      ),
    },
    {
      title: "Name",
      dataIndex: "name",
      key: "name",
      width: "20%",
      filteredValue: filters.name ? ([filters.name] as string[]) : null,
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
        clearFilters,
      }: any) => (
        <div className="filter-dropdown">
          <Input
            ref={codeInputRef}
            placeholder="Search name"
            value={selectedKeys[0]}
            onChange={(e) => {
              const value = e.target.value;
              setSelectedKeys(value ? [value] : []);
              if (!value) {
                clearFilters();
                confirm();
                handleFilterChange("name", "All"); // Reset to show all data
              }
            }}
            onPressEnter={() => {
              confirm();
              handleFilterChange("name", selectedKeys[0]); // Update URL
            }}
            autoFocus
            suffix={
              selectedKeys[0] ? (
                <span
                  onClick={() => {
                    setSelectedKeys([]);
                    clearFilters();
                    confirm();
                    handleFilterChange("name", "All"); // Reset URL filter
                  }}
                  className="search-filter-clear-btn"
                >
                  ✖
                </span>
              ) : null
            }
          />
        </div>
      ),
      filterDropdownProps: {
        onOpenChange: (visible: boolean) => {
          if (visible) {
            setTimeout(() => codeInputRef.current?.focus(), 100); // Focus the input field
          }
        },
      },
      filterIcon: (filtered: boolean) => (
        <div className={`filter-icon ${filtered ? "active" : "inactive"}`}>
          <SearchOutlined />
        </div>
      ),
    },
    {
      title: "Phone",
      dataIndex: "phone",
      key: "phone",
      width: "15%",
      filteredValue: filters.phone ? [filters.phone] : null,
      filterDropdown: ({
        setSelectedKeys,
        selectedKeys,
        confirm,
      }: FilterDropdownProps) => (
        <div className="filter-dropdown">
          <Input
            ref={codeInputRef}
            placeholder="Enter Customer Name"
            value={selectedKeys[0] || ""}
            onChange={(e) =>
              setSelectedKeys(e.target.value ? [e.target.value] : [])
            }
            onPressEnter={() => {
              confirm();
              handleFilterChange("phone", String(selectedKeys[0]));
            }}
            autoFocus
            suffix={
              <span
                onClick={() => {
                  setSelectedKeys([]);

                  confirm();
                  handleFilterChange("phone", "All");
                }}
                className="search-filter-clear-btn"
              >
                ✖
              </span>
            }
          />
        </div>
      ),
      filterDropdownProps: {
        onOpenChange: (visible: boolean) => {
          if (visible) {
            setTimeout(() => codeInputRef.current?.focus(), 100); // Focus the input field
          }
        },
      },
      filterIcon: (filtered: boolean) => (
        <div className={`filter-icon ${filtered ? "active" : "inactive"}`}>
          <SearchOutlined />
        </div>
      ),
    },
    {
      title: "Created Date",
      dataIndex: "created_at",
      key: "created_at",
      width: "15%",
      render: (createdAt: string) =>
        dayjs(createdAt).format("DD MMM YYYY, hh:mm A"), // 12-hour format  //for 24-hour format: dayjs(createdAt).format("DD MMM 'YYYY, HH:mm:ss")
    },
    {
      title: "Updated Date",
      dataIndex: "updated_at",
      key: "updated_at",
      width: "15%",
      render: (createdAt: string) =>
        dayjs(createdAt).format("DD MMM YYYY, hh:mm A"), // 12-hour format  //for 24-hour format: dayjs(createdAt).format("DD MMM 'YYYY, HH:mm:ss")
    },
    {
      title: "Active",
      dataIndex: "is_active",
      key: "is_active",
      width: "10%",
      filteredValue:
        filters.is_active !== undefined
          ? [filters.is_active === "True" ? "Yes" : "No"]
          : null,
      filterDropdown: (props: any) => (
        <FilterMenu
          {...props}
          filterKey="is_active"
          options={kioskIs_Active_Options.map((status: StatusOption) => ({
            label: status.label,
            value: status.value,
          }))}
          handleFilterChange={handleFilterChange}
        />
      ),
      render: (isActive: boolean) => (isActive ? "Yes" : "No"),
    },
  ];

  const textFormat = (text: string) => {
    if (!text) return "-";

    return text
      .replace(/[^a-zA-Z0-9 ]/g, " ")
      .trim()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(" ");
  };

  // Error handling
  if (!loading && error) {
    return (
      <div className="error-container">
        <Alert message="Error" description={error} type="error" showIcon />
        <div className="retry-button d-flex justify-content-center align-items-center mt-3">
          <Button type="primary" onClick={() => window.location.reload()}>
            {Retry}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div className="title-and-add-btn d-flex justify-content-between align-items-center flex-wrap heading-title">
        <div>{Store_List}</div>
        <div className="d-flex flex-wrap gap-2">
          <Button
            type="primary"
            className="add-store me-2"
            onClick={() => navigate("/create-store")}
          >
            {Add_New_Store}
          </Button>
          <Button
            type="primary"
            className="add-store"
            onClick={() => navigate("/stores/upload-csv")}
          >
            Upload CSV
          </Button>
        </div>
      </div>
      <div className="pt-4">
        <FilterButtons
          showClearButtons={showClearButtons}
          appliedFilters={appliedFilters}
          clearAllFilters={clearAllFilters}
          clearFilter={clearFilter}
          formatFilterValue={textFormat}
          filters={filters}
        />
      </div>
      <div className="pt-2 mt-2">
        <DataTable
          className="mt-2"
          columns={columns}
          dataSource={stores}
          rowKey="id"
          pagination={false}
          loading={loading}
          scroll={{ x: 1000 }}
        />

        <CommonPagination
          current={currentPage}
          pageSize={pageSize}
          total={totalCount}
          showSizeChanger
          onShowSizeChange={handlePageChange}
          onChange={handlePageChange}
        />
      </div>
    </div>
  );
};

export default StoresList;
